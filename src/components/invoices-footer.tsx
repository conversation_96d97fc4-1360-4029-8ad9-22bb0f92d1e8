'use client';

import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import { Pagination } from '@ourtrip/ui';
import { InvoiceResponse } from '@/lib/definitions/invoice';

const InvoicesFooter = ({ invoices }: { invoices: InvoiceResponse | null }) => {
  const router = useRouter();
  const params = useSearchParams();

  const handleChange = (newPage: number) => {
    const newParams = new URLSearchParams(params.toString());

    newParams.set('page', (newPage - 1).toString());
    router.push(`?${newParams.toString()}`);
  };

  return (
    <Pagination
      page={params.get('page') ? parseInt(params.get('page') || '0') + 1 : 1}
      total={invoices ? invoices.totalPages : 0}
      onChange={handleChange}
    />
  );
};

export default InvoicesFooter;
