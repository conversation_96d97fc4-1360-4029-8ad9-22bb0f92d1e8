'use client';

import { Stars } from '@ourtrip/ui';
import { FC } from 'react';
import { Divider } from '@ourtrip/ui';
import { BookingRoom, BookingStatusEnum } from '@/lib/definitions/booking';
import ReservationCardRoom from '@/components/reservation-card-room';
import { Badge } from '@ourtrip/ui';
import { Room } from '@/lib/definitions/hotel';
import { mountRoomDistributionStringFromObject } from '@/lib/utils/search';

interface IReservationCard {
  stars?: number;
  name: string;
  address: string;
  channel: string;
  hotelId: string;
  currencySymbol: string;
  providerAccount?: string;
  reference?: string;
  reservationCode?: string;
  distributionText?: string;
  totalPrice?: string;
  status?: BookingStatusEnum;
  statusDisplay?: string;
  image?: string;
  checkin: string;
  checkout: string;
  rooms: BookingRoom[] | Room[];
  border?: boolean;
  backgroundColor?: string;
  expandable?: boolean;
}

const ReservationCard: FC<IReservationCard> = ({
  stars,
  name,
  image,
  address,
  channel,
  hotelId,
  providerAccount,
  reference,
  reservationCode,
  distributionText,
  currencySymbol,
  totalPrice,
  status,
  statusDisplay,
  checkin,
  checkout,
  rooms
}: IReservationCard) => {
  const getStatusType = () => {
    switch (status) {
      case BookingStatusEnum.CREATED:
        return 'info';
      case BookingStatusEnum.BOOKING:
        return 'info';
      case BookingStatusEnum.BOOKED:
        return 'success';
      case BookingStatusEnum.FAILED:
        return 'danger';
      case BookingStatusEnum.DISCONTINUED:
        return 'warning';
      case BookingStatusEnum.PENDING_CANCEL:
        return 'warning';
      case BookingStatusEnum.CANCELED:
        return 'danger';
      default:
        return 'info';
    }
  };

  return (
    <div className='flex flex-col gap-2'>
      <div className='overflow-hidden flex flex-col gap-6 rounded-default p-4 bg-white'>
        <div>
          <div className='flex flex-col gap-6'>
            <div className='flex flex-col md:flex-row gap-6'>
              {image && (
                <img
                  className='w-48 h-48 object-cover rounded-inner'
                  src={image}
                  alt='Imagem de fundo do hotel'
                />
              )}
              <div className='flex flex-col gap-3 justify-between flex-1'>
                <div className='flex flex-col'>
                  {stars ? (
                    <div className='mb-1'>
                      <Stars rate={stars} />
                    </div>
                  ) : null}
                  <h2
                    className='text-lg text-primary-900 font-semibold cursor-pointer hover:underline'
                    onClick={() =>
                      window.open(
                        `https://ourtrip.com.br/hotel/${hotelId}?distribution=${mountRoomDistributionStringFromObject(
                          rooms.map(room => ({
                            adults: room.guests.filter(guest => !guest.child)
                              .length,
                            kids: room.guests
                              .filter(guest => guest.child)
                              .map(guest => guest.age)
                          }))
                        )}&checkin=${checkin
                          .replaceAll('/', '-')
                          .split('-')
                          .reverse()
                          .join('-')}&checkout=${checkout
                          .replaceAll('/', '-')
                          .split('-')
                          .reverse()
                          .join(
                            '-'
                          )}&destination=${name}&code=${hotelId}&group=HOTEL`,
                        '_blank'
                      )
                    }
                  >
                    {name} #{hotelId}
                  </h2>
                  <div className='text-gray-500 text-sm line-clamp-2'>
                    {address}
                  </div>
                </div>
                <div className='flex gap-2'>
                  <div className='flex flex-col'>
                    <p className='text-xs text-gray-500'>Dia do Check-in</p>
                    <h3 className='font-semibold'>{checkin}</h3>
                  </div>
                  <Divider />
                  <div className='flex flex-col'>
                    <p className='text-xs text-gray-500'>Dia do Check-out</p>
                    <h3 className='font-semibold'>{checkout}</h3>
                  </div>
                </div>
              </div>
              {(statusDisplay ||
                distributionText ||
                totalPrice ||
                reservationCode) && (
                <div className='w-full flex flex-col items-end justify-between flex-1'>
                  <div className='flex flex-col items-end text-right justify-center mb-3'>
                    {statusDisplay ? (
                      <Badge type={getStatusType()}>{statusDisplay}</Badge>
                    ) : null}
                    {distributionText && (
                      <p className='text-sm text-gray-500 mt-2 text-right'>
                        {distributionText}
                      </p>
                    )}
                  </div>
                  {reservationCode && (
                    <div className='flex flex-col items-end'>
                      <p className='text-xs text-gray-500'>Código da reserva</p>
                      <h3 className='text-primary-900 font-medium'>
                        {reservationCode}
                      </h3>
                    </div>
                  )}
                </div>
              )}
            </div>
            <div className='flex flex-wrap gap-6 bg-gray-50 px-3 py-2 rounded-lg text-sm'>
              <div>
                <span className='text-gray-500'>Credencial</span>
                <p className='text-primary-900 font-medium'>
                  {providerAccount || '-'}
                </p>
              </div>
              <Divider />
              <div>
                <span className='text-gray-500'>Localizador</span>
                <p className='text-primary-900 font-medium'>
                  {reference || '-'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className='grid grid-cols-1 min-[1700px]:grid-cols-2 gap-2'>
        {rooms.map((room, index) => {
          const isLastElement = index === rooms.length - 1;
          const isOddTotal = rooms.length % 2 !== 0;
          const shouldSpanTwoColumns = isLastElement && isOddTotal;

          return (
            <ReservationCardRoom
              key={room.accommodationId + room.accommodationIndex}
              className={shouldSpanTwoColumns ? 'min-[1700px]:col-span-2' : ''}
              room={room}
            />
          );
        })}
      </div>
    </div>
  );
};

export default ReservationCard;
