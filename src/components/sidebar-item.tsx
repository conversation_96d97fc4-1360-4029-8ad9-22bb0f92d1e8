'use client';

import { ElementType, useState } from 'react';
import { redirect, usePathname } from 'next/navigation';
import { CaretLeft, IconProps } from '@phosphor-icons/react';

export type SidebarItemProps = {
  href?: string;
  icon?: ElementType<IconProps>;
  label?: string;
  items?: SidebarItemProps[];
  type?: 'divider' | 'title';
  tag?: string;
  onClick?: () => void;
};

const SidebarItem = ({
  href,
  icon: Icon,
  label,
  items,
  onClick,
  type,
  tag
}: SidebarItemProps) => {
  if (type === 'divider') {
    return <div className='my-1 border-b border-gray-200/70 ml-4' />;
  }

  if (type === 'title') {
    return <p className='text-xs text-gray-500 ml-1 mb-1 mt-2'>{label}</p>;
  }

  const pathname = usePathname();
  const isActive = pathname === href;
  const [isOpen, setIsOpen] = useState<boolean>(
    pathname.includes(href!) && !!items?.length
  );

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();

    if (items && items.length) {
      setIsOpen(!isOpen);
      return;
    }

    if (onClick) {
      onClick();
      return;
    }

    if (href) {
      redirect(href);
    }
  };

  return (
    <div
      onClick={handleClick}
      className={`
        flex flex-col px-4 py-3 rounded-inner
        transition-colors duration-200 cursor-pointer
        ${
          isActive && !items?.length
            ? 'bg-gray-100 text-primary-500'
            : !!items?.length
              ? 'text-gray-500'
              : 'hover:bg-gray-100 text-gray-500'
        }
      `}
    >
      <div className='flex items-center justify-between'>
        <div className='flex gap-3 items-center'>
          {Icon && (
            <div className='flex-none text-gray-500 items-center justify-center'>
              <Icon size={18} />
            </div>
          )}
          <h3 className='font-medium leading-4 text-sm text-primary-900'>
            {label}
          </h3>
          {tag && (
            <div className='text-xs font-medium bg-primary-100 rounded-inner px-2 border border-primary-200'>
              <p className='leading-4 text-primary-600'>{tag}</p>
            </div>
          )}
        </div>
        {items && items.length && (
          <CaretLeft
            className={`transition-all ${isOpen ? '-rotate-90' : ''}`}
          />
        )}
      </div>
      {items && items.length && isOpen && (
        <div className='flex flex-col mt-3'>
          {items.map(item => (
            <SidebarItem
              key={item.label}
              type={item.type}
              href={item.href}
              label={item.label}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default SidebarItem;
