'use client';

import {
  <PERSON>ge,
  Tooltip,
  Toolt<PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger
} from '@ourtrip/ui';
import { Check, Info } from '@phosphor-icons/react';

const RunDetailsStatus = ({ hotel }: { hotel: any }) => {
  if (hotel.price > hotel.target_price || hotel.success) {
    return (
      <Badge
        className='flex w-min'
        type={hotel.success ? 'success' : 'danger'}
        icon={
          hotel.success ? (
            <Check />
          ) : (
            <div className='h-4 w-4 overflow-hidden flex items-center justify-center'>
              <TooltipProvider delayDuration={0} skipDelayDuration={1000}>
                <Tooltip>
                  <TooltipContent className='bg-white text-gray-800 p-2 rounded shadow-md'>
                    {hotel.message || 'Erro desconhecido'}
                  </TooltipContent>
                  <TooltipTrigger className='h-4 w-4'>
                    <Info className='h-4 w-4' />
                  </TooltipTrigger>
                </Tooltip>
              </TooltipProvider>
            </div>
          )
        }
        size='small'
      >
        {hotel.success ? 'Sucesso' : 'Falha'}
      </Badge>
    );
  }

  if (hotel.price <= hotel.target_price) {
    return (
      <Badge className='flex w-min' type='info' size='small'>
        Melhor preço
      </Badge>
    );
  }
};

export default RunDetailsStatus;
