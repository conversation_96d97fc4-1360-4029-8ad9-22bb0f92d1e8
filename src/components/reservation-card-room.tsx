import { BookingRoom } from '@/lib/definitions/booking';
import {
  Baby,
  Bed,
  Check,
  ForkKnife,
  User,
  Warning
} from '@phosphor-icons/react';
import { convertStringToDefaultFormatedDate } from '@/lib/utils/date';
import { Room } from '@/lib/definitions/hotel';
import { Badge } from '@ourtrip/ui';

interface IReservationCardRoom {
  className?: string;
  room: BookingRoom | Room;
}

const ReservationCardRoom = ({ className, room }: IReservationCardRoom) => {
  return (
    <div
      className={`w-full flex flex-col justify-between bg-white rounded-inner p-4 gap-4 ${className}`}
    >
      <div className='flex items-center gap-4'>
        <div className='flex flex-col gap-3 justify-between items-start'>
          <div className='flex items-center gap-2 flex-wrap'>
            <p className='text-gray-500'>
              Quarto {room.accommodationIndex + 1}
            </p>
            {room.cancellationPolicies.refundable ? (
              <Badge
                icon={<Check weight='bold' size={14} />}
                type='success'
                size='small'
              >
                Reembolsável
              </Badge>
            ) : (
              <Badge
                icon={<Warning weight='bold' size={14} />}
                type='warning'
                size='small'
              >
                Não reembolsável
              </Badge>
            )}
            <Badge icon={<ForkKnife />} type='info' size='small'>
              {room.mealPlanDisplay}
            </Badge>
          </div>
          <div className='flex items-center gap-3'>
            <div className='w-11 h-11 bg-gray-100 rounded-full flex items-center justify-center'>
              <Bed size={18} />
            </div>
            <div className='flex flex-col gap-1'>
              <div className='flex items-center'>
                <h3 className='text-primary-900 font-semibold'>
                  {room.accommodationName}
                </h3>
              </div>
              {room.cancellationPolicies.refundable ? (
                <p className='text-success-600 text-sm leading-3'>
                  Cancelamento grátis até{' '}
                  {convertStringToDefaultFormatedDate(
                    room.cancellationPolicies.cancellationLimitDate
                  )}
                </p>
              ) : null}
            </div>
          </div>
        </div>
      </div>
      <div className='flex flex-col gap-2'>
        {room.guests.map(guest => (
          <div
            key={guest.id}
            className='flex items-center justify-between gap-2 text-sm text-gray-500 bg-gray-100 px-3 py-2 rounded-inner'
          >
            <div className='flex items-center gap-3'>
              {guest.child ? <Baby size={16} /> : <User size={16} />}
              <p className='text-primary-900 text-sm font-medium'>
                {guest.name} {guest.surname}
              </p>
            </div>
            {guest.child ? (
              <p className='text-gray-500 text-sm'>
                {guest.age} ano{guest.age > 1 ? 's' : ''}
              </p>
            ) : (
              <p>-</p>
            )}
            <p className='text-gray-500 text-sm'>{guest.document}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ReservationCardRoom;
