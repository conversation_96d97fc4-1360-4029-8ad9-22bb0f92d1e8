'use client';

import { Invoice } from '@/lib/definitions/invoice';
import { getInvoiceFile } from '@/services/invoice';
import { Button, Popover, PopoverContent, PopoverTrigger } from '@ourtrip/ui';
import {
  CircleNotch,
  DotsThree,
  Download,
  Eye,
  ReceiptX,
  X
} from '@phosphor-icons/react';
import { useState } from 'react';
import { toast } from 'sonner';

const InvoicesActions = ({ invoice }: { invoice: Invoice }) => {
  const [isLoadingView, setIsLoadingView] = useState<boolean>(false);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [selectedInvoice, setSelectedInvoice] = useState<ArrayBuffer | null>(
    null
  );

  const getFile = async (id: string): Promise<ArrayBuffer | null> => {
    try {
      const fileDataResponse = await getInvoiceFile(id);

      if (fileDataResponse.error) {
        throw new Error(fileDataResponse.error);
      }

      return fileDataResponse.data!;
    } catch (error: any) {
      toast.error(error.message);
      return null;
    }
  };

  const handleSetFilePreview = async (id: string) => {
    setIsLoadingView(true);
    const fileDataResponse = await getFile(id);

    if (!fileDataResponse) {
      toast.error('Arquivo não encontrado');
    } else {
      setSelectedInvoice(fileDataResponse);
    }

    setIsLoadingView(false);
  };

  const handleDownloadFile = async (id: string) => {
    setIsDownloading(true);
    const fileDataResponse = await getFile(id);

    if (!fileDataResponse) {
      toast.error('Arquivo não encontrado');
    } else {
      const blob = new Blob([fileDataResponse], {
        type: 'application/pdf'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `nfe-${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      toast.success('Arquivo baixado com sucesso');
    }

    setIsDownloading(false);
  };

  return (
    <div className='flex gap-2 justify-end'>
      <Popover>
        <PopoverTrigger asChild>
          <Button size='small' color='white' className='p-1'>
            <DotsThree size={18} weight='bold' />
          </Button>
        </PopoverTrigger>
        <PopoverContent align='end' className='rounded-default shadow-2xl'>
          <p className='text-sm text-gray-500 font-medium mb-2'>Ações</p>
          <div className='flex flex-col'>
            <div
              className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
              onClick={() => handleSetFilePreview(invoice.idNfe)}
            >
              {isLoadingView ? (
                <CircleNotch className='animate-spin' weight='bold' />
              ) : (
                <Eye />
              )}
              <p className='text-sm text-primary-900 font-medium'>Ver</p>
            </div>
            <div
              className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'
              onClick={() => handleDownloadFile(invoice.idNfe)}
            >
              {isDownloading ? (
                <CircleNotch className='animate-spin' weight='bold' />
              ) : (
                <Download />
              )}
              <p className='text-sm text-primary-900 font-medium'>Baixar</p>
            </div>
            <div className='flex items-center gap-2 hover:bg-gray-100 px-3 py-2 rounded-inner cursor-pointer'>
              <ReceiptX />
              <p className='text-sm text-primary-900 font-medium'>Cancelar</p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default InvoicesActions;
