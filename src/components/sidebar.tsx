'use client';

import { Button } from '@ourtrip/ui';
import { SidebarSimple } from '@phosphor-icons/react';
import Image from 'next/image';
import SidebarItem from './sidebar-item';
import SidebarUser from './sidebar-user';
import {
  getSidebarItemsByPermissions,
  sidebarItems
} from '@/lib/definitions/sidebar-items';

export const Sidebar = ({
  user
}: {
  user: {
    id: string;
    name: string;
    email: string;
    profiles: string[];
    privileges: string[];
  };
}) => {
  const menuItems = getSidebarItemsByPermissions(
    sidebarItems,
    user?.privileges || []
  );

  return (
    <div className='w-72 flex flex-none flex-col bg-white p-6 rounded-default justify-between select-none'>
      <div className='flex flex-col gap-4 overflow-hidden'>
        <div className='w-full flex justify-between items-center pl-3'>
          <Image
            src='/horizontal_icon_text_blue.svg'
            alt='OurTrip'
            width={96}
            height={26}
          />
          <Button color='white' size='icon'>
            <SidebarSimple size={18} />
          </Button>
        </div>
        <nav className='overflow-auto scrollbar'>
          <ul className='flex flex-col'>
            {menuItems.map(
              item =>
                item && (
                  <SidebarItem
                    key={item?.label}
                    href={item?.href}
                    icon={item?.icon}
                    label={item?.label || ''}
                    items={item?.items}
                    tag={item?.tag}
                  />
                )
            )}
          </ul>
        </nav>
      </div>
      <SidebarUser user={user} />
    </div>
  );
};

export default Sidebar;
