'use client';

import React from 'react';
import {
  Button,
  Input,
  Sheet,
  She<PERSON><PERSON>lose,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger
} from '@ourtrip/ui';
import { Sliders, X } from '@phosphor-icons/react';
import { useForm } from 'react-hook-form';
import { useSearchParams, useRouter } from 'next/navigation';
import {
  defaultInvoiceFilters,
  InvoiceFilters,
  InvoiceResponse
} from '@/lib/definitions/invoice';

const InvoicesHeader = ({ invoices }: { invoices: InvoiceResponse | null }) => {
  const router = useRouter();
  const params = useSearchParams();

  const { register, handleSubmit, resetField } = useForm<InvoiceFilters>({
    defaultValues: {
      page: parseInt(params.get('page') || '0', 10),
      size: parseInt(params.get('size') || '10', 10),
      orderCode: params.get('orderCode') || '',
      reservationCode: params.get('reservationCode') || '',
      numeroNfe: params.get('numeroNfe') || '',
      dataEmissaoInicio: params.get('dataEmissaoInicio') || '',
      dataEmissaoFim: params.get('dataEmissaoFim') || '',
      dataCompetenciaInicio: params.get('dataCompetenciaInicio') || '',
      dataCompetenciaFim: params.get('dataCompetenciaFim') || ''
    }
  });

  const handleFilter = (filters: InvoiceFilters) => {
    Object.keys(filters).forEach(key => {
      if (
        filters[key as keyof InvoiceFilters] === '' ||
        filters[key as keyof InvoiceFilters] === null
      ) {
        delete filters[key as keyof InvoiceFilters];
      }
    });

    const orderedKeys = Object.keys(defaultInvoiceFilters) as Array<
      keyof InvoiceFilters
    >;

    const params = new URLSearchParams();
    orderedKeys.forEach(key => {
      const val = filters[key];
      if (val !== null && val !== '' && val !== undefined) {
        params.set(key, String(val));
      }
    });

    router.push(`?${params.toString()}`);
  };

  const handleRemoveFilter = (key: string) => {
    const newParams = new URLSearchParams();
    for (const [k, v] of params.entries()) {
      if (k !== key) {
        newParams.set(k, v);
      }
    }

    resetField(key as keyof InvoiceFilters, { defaultValue: '' });
    router.push(`?${newParams.toString()}`);
  };

  const handleClearFilters = () => {
    const newParams = new URLSearchParams();
    newParams.set('page', params.get('page') || '0');
    newParams.set('size', params.get('size') || '10');

    const keys = Object.keys(defaultInvoiceFilters).filter(
      key => key !== 'page' && key !== 'size'
    ) as Array<keyof InvoiceFilters>;
    keys.forEach(key => {
      resetField(key, { defaultValue: '' });
    });

    router.push(`?${newParams.toString()}`);
  };

  return (
    <div className='flex items-center justify-between'>
      <div className='flex items-end gap-2'>
        <Sheet>
          <SheetTrigger asChild>
            <Button color='white' size='small'>
              <Sliders />
              Filtrar
            </Button>
          </SheetTrigger>
          <SheetContent>
            <form onSubmit={handleSubmit(handleFilter)}>
              <SheetHeader>
                <SheetTitle>Filtros</SheetTitle>
              </SheetHeader>
              <div className='flex flex-col gap-2 mt-2'>
                <Input
                  {...register('numeroNfe')}
                  color='gray'
                  placeholder='Número da NF-e'
                />
                <div className='flex gap-2'>
                  <Input
                    {...register('orderCode')}
                    color='gray'
                    placeholder='Código do pedido (OT)'
                  />
                  <Input
                    {...register('reservationCode')}
                    color='gray'
                    placeholder='Código da reserva (OTB)'
                  />
                </div>
                <div className='flex gap-2'>
                  <Input
                    {...register('dataEmissaoInicio')}
                    color='gray'
                    type='date'
                    placeholder='Data de emissão início'
                  />
                  <Input
                    {...register('dataEmissaoFim')}
                    color='gray'
                    type='date'
                    placeholder='Data de emissão fim'
                  />
                </div>
                <div className='flex gap-2'>
                  <Input
                    {...register('dataCompetenciaInicio')}
                    color='gray'
                    type='date'
                    placeholder='Data de competência início'
                  />
                  <Input
                    {...register('dataCompetenciaFim')}
                    color='gray'
                    type='date'
                    placeholder='Data de competência fim'
                  />
                </div>
                <SheetFooter>
                  <SheetClose asChild>
                    <div className='w-full flex gap-2 mt-2'>
                      <Button
                        type='button'
                        variant='outline'
                        onClick={handleClearFilters}
                      >
                        Limpar
                      </Button>
                      <Button type='submit' color='primary' className='w-full'>
                        Filtrar
                      </Button>
                    </div>
                  </SheetClose>
                </SheetFooter>
              </div>
            </form>
          </SheetContent>
        </Sheet>
        <div className='flex flex-wrap gap-2'>
          {Array.from(params.entries())
            .filter(([key]) => key !== 'page' && key !== 'size')
            .map(([key, value]) => (
              <div
                key={key}
                className='bg-white rounded-inner flex items-center gap-2 px-3 py-1.5'
              >
                <X
                  size={14}
                  className='text-gray-500 cursor-pointer hover:scale-125 transition-transform'
                  onClick={() => handleRemoveFilter(key)}
                />
                <p className='text-sm'>{value}</p>
              </div>
            ))}
        </div>
      </div>
      <p className='text-sm text-gray-500'>
        Exibindo {invoices?.numberOfElements} de {invoices?.totalElements} notas
        fiscais
      </p>
    </div>
  );
};

export default InvoicesHeader;
