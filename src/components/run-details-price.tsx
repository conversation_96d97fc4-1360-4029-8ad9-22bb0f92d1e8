'use client';

import { convertNumberToCurrency } from '@/lib/utils/currency';
import { CaretRight, Target } from '@phosphor-icons/react';

const RunDetailsPrice = ({ hotel }: any) => {
  return (
    <div className='flex items-center gap-2'>
      <p
        className={`text-sm font-medium ${hotel.price > hotel.target_price ? 'text-red-400' : 'text-green-400'} ${hotel.new_price ? 'line-through' : ''}`}
      >
        {`R$ ${convertNumberToCurrency('BRL', hotel.price * 100)}`}
      </p>
      <CaretRight size={12} />
      <p className='text-sm text-primary-900 font-medium'>
        {hotel.new_price
          ? `R$ ${convertNumberToCurrency('BRL', hotel.new_price * 100)}`
          : '-'}
      </p>
    </div>
  );
};

export default RunDetailsPrice;
