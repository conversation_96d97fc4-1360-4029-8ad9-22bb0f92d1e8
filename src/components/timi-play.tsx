'use client';

import { RunRequest } from '@/lib/definitions/timi';
import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@ourtrip/ui';
import { Plus, X } from '@phosphor-icons/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

const TimiPlay = ({
  trivagoStatus,
  timiStatus,
  intelligenceStatus
}: {
  trivagoStatus: boolean;
  timiStatus: boolean;
  intelligenceStatus: boolean;
}) => {
  const router = useRouter();
  const [isSending, setIsSending] = useState<boolean>(false);
  const [headers, setHeaders] = useState<string[]>([]);

  const { register, handleSubmit, watch, reset, setValue } =
    useForm<RunRequest>({
      defaultValues: {
        filters: [{ column: 'Partner Country ID', value: 'BR' }],
        batchSize: 10,
        lengthOfStay: 1,
        timeToTravel: 3
      }
    });

  const filters = watch('filters');

  const run = async (values: RunRequest) => {
    try {
      const response = await fetch(`https://timi-service.ourtrip.travel/run`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          filters: values.filters,
          batchSize: values.batchSize,
          lengthOfStay: values.lengthOfStay,
          timeToTravel: values.timeToTravel
        })
      });

      if (!response.ok) {
        return {
          error: 'Falha ao executar'
        };
      }

      return {
        data: response.ok
      };
    } catch (error: any) {
      return {
        error: error.message
      };
    }
  };

  const handleSendInventory = async (values: RunRequest) => {
    setIsSending(true);

    try {
      const response = await run({
        filters: values.filters,
        batchSize: values.batchSize,
        lengthOfStay: values.lengthOfStay,
        timeToTravel: values.timeToTravel
      });

      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success('Execução iniciada com sucesso');
        reset({ filters: [], batchSize: 10, lengthOfStay: 1, timeToTravel: 3 });
        router.refresh();
      }
    } catch (error: any) {
      console.error('Submission error:', error);
      toast.error('Erro ao enviar arquivo de inventário', error.message);
    }

    setIsSending(false);
  };

  useEffect(() => {
    const fetchHeaders = async () => {
      const response = await fetch(
        `https://intelligence-service.ourtrip.travel/inventory/filters`
      );
      const data = await response.json();
      setHeaders(data);
    };

    fetchHeaders();
  }, []);

  return (
    <form
      className='flex flex-col bg-white rounded-default p-3 gap-2'
      onSubmit={handleSubmit(handleSendInventory)}
    >
      <p className='text-sm text-gray-500'>Executar</p>
      {headers.length > 0 && (
        <div className='w-full flex flex-col gap-2 mb-3'>
          <div className='flex items-center gap-2'>
            <Input
              type='number'
              placeholder='Tamanho do Lote'
              label='Lote'
              {...register('batchSize', { valueAsNumber: true })}
              color='gray'
              className='w-32'
            />
            <Input
              type='number'
              placeholder='Duração da Estadia'
              label='LOS'
              {...register('lengthOfStay', { valueAsNumber: true })}
              color='gray'
              className='w-32'
            />
            <Input
              type='number'
              placeholder='Tempo de Viagem'
              label='TTT'
              {...register('timeToTravel', { valueAsNumber: true })}
              color='gray'
              className='w-32'
            />
          </div>
          <p className='text-sm text-gray-500 mt-2'>Filtros</p>
          {filters?.map((filter, index) => (
            <div key={index} className='flex gap-2 items-center'>
              <Select
                {...register(`filters.${index}.column`)}
                onValueChange={value =>
                  setValue(`filters.${index}.column`, value)
                }
                defaultValue={filter.column}
              >
                <SelectTrigger className='flex-2'>
                  <SelectValue placeholder='Selecione a Coluna' />
                </SelectTrigger>
                <SelectContent>
                  {headers.map(header => (
                    <SelectItem key={header} value={header}>
                      {header}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Input
                placeholder='Valor'
                {...register(`filters.${index}.value`)}
                color='gray'
                className='flex-4'
              />
              <Button
                color='gray'
                size='icon'
                onClick={() => {
                  const newFilters = [...filters];
                  newFilters.splice(index, 1);
                  setValue('filters', newFilters);
                }}
              >
                <X />
              </Button>
            </div>
          ))}
          <Button
            color='gray'
            size='small'
            onClick={() => {
              const newFilters = [...filters];
              newFilters.push({ column: '', value: '' });
              setValue('filters', newFilters);
            }}
            type='button'
          >
            <Plus size={14} />
          </Button>
        </div>
      )}
      <Button
        color='primary'
        disabled={
          !trivagoStatus ||
          !timiStatus ||
          !intelligenceStatus ||
          !headers.length
        }
        loading={isSending}
        type='submit'
      >
        {!trivagoStatus ? 'Serviço indisponível' : 'Iniciar'}
      </Button>
    </form>
  );
};

export default TimiPlay;
