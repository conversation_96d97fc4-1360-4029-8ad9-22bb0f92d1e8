'use client';

import { Divider } from '@ourtrip/ui';
import { Calendar, CheckCircle } from '@phosphor-icons/react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useRouter } from 'next/navigation';
dayjs.extend(utc);

const formatDuration = (endedAt: string, createdAt: string) => {
  const duration = dayjs(endedAt)
    .utc(true)
    .diff(dayjs(createdAt).utc(true), 'second');
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  const seconds = duration % 60;

  const formattedHours = hours.toString().padStart(2, '0');
  const formattedMinutes = minutes.toString().padStart(2, '0');
  const formattedSeconds = seconds.toString().padStart(2, '0');

  return hours > 0
    ? `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
    : `${formattedMinutes}:${formattedSeconds}`;
};

const TimiRunItem = ({ run }: { run: any }) => {
  const router = useRouter();
  const duration = dayjs(run.ended_at).diff(dayjs(run.created_at), 'second');
  const averageTimePerHotel = duration / run.processed_hotels_count;

  const parsedFilters = run?.filters
    ? (() => {
        try {
          const validJson = run.filters.replace(/'/g, '"');
          return JSON.parse(validJson);
        } catch (error) {
          return [];
        }
      })()
    : [];

  return (
    <div
      className='flex flex-col bg-white rounded-default p-3 cursor-pointer'
      onClick={() => router.push(`/timi/${run.id}`)}
    >
      <div className='flex justify-between items-center'>
        <div className='flex items-center gap-1'>
          <CheckCircle className='text-success-500' />
          <p className='text-sm font-medium'>
            Execução{' '}
            <span className='text-xs text-gray-500'>
              #{run.id.split('-')[0]}
            </span>
          </p>
        </div>
      </div>
      <div className='flex justify-between mt-3'>
        <div className='flex flex-col gap-3'>
          <div className='flex gap-6'>
            <div>
              <p className='text-sm text-gray-500'>Tempo</p>
              <h3 className='text-primary-900 font-medium'>
                {formatDuration(run.ended_at, run.created_at)}
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Média</p>
              <h3 className='text-primary-900 font-medium'>
                {averageTimePerHotel.toFixed(2)}
                s/hotel
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Hotéis obtidos</p>
              <h3 className='text-primary-900 font-medium'>
                {run.total_changed_hotels}
              </h3>
            </div>

            <div>
              <p className='text-sm text-gray-500'>Beat</p>
              <h3 className='text-primary-900 font-medium'>
                {run.total_hotels_with_lower_price}
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Beat Rate</p>
              <h3 className='text-primary-900 font-medium'>
                {(
                  (run.total_hotels_with_lower_price /
                    run.total_changed_hotels) *
                  100
                ).toFixed(2)}
                %
              </h3>
            </div>
          </div>
          <div className='flex gap-6'>
            <div>
              <p className='text-sm text-gray-500'>Hotéis</p>
              <h3 className='text-primary-900 font-medium'>
                {run.hotels_count}
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Processados</p>
              <h3 className='text-primary-900 font-medium'>
                {run.processed_hotels_count}
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Progresso</p>
              <h3 className='text-primary-900 font-medium'>
                {(
                  (run.processed_hotels_count / run.hotels_count) *
                  100
                ).toFixed(0)}
                %
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Taxa de sucesso</p>
              <h3 className='text-primary-900 font-medium'>
                {run.success_rate_percent.toFixed(2)}%
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Sucesso</p>
              <h3 className='text-primary-900 font-medium'>
                {run.total_successful_hotels}
              </h3>
            </div>
            <div>
              <p className='text-sm text-gray-500'>Falha</p>
              <h3 className='text-primary-900 font-medium'>
                {run.total_unsuccessful_hotels}
              </h3>
            </div>
          </div>
        </div>
      </div>
      <Divider orientation='horizontal' className='mt-3' />
      <div className='flex flex-col gap-2 mt-4'>
        <div className='flex gap-1'>
          <p className='text-sm text-gray-500 flex items-center gap-1'>
            <Calendar />
            Iniciada em
          </p>
          <p className='text-sm font-medium'>
            {dayjs(run.created_at).utc(true).format('DD MMM HH:mm')}
          </p>
        </div>
        <div className='flex gap-1'>
          <p className='text-sm text-gray-500 flex items-center gap-1'>
            <CheckCircle />
            Finalizada em
          </p>
          <p className='text-sm font-medium'>
            {dayjs(run.ended_at).utc(true).format('DD MMM HH:mm')}
          </p>
        </div>
      </div>
      <div className='flex flex-col gap-2 mt-4'>
        <div className='flex flex-col gap-2'>
          {parsedFilters.map((filter: any, index: number) => (
            <div
              key={index}
              className='flex gap-1 items-center bg-gray-100 p-2 rounded-inner'
            >
              <p className='text-sm text-gray-500'>{filter.column}:</p>
              <p className='text-sm text-gray-500'>{filter.value}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TimiRunItem;
