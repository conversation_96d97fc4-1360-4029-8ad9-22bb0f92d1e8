'use client';

import { Order } from '@/lib/definitions/order';
import { orderPaymentApi, orderPaymentStatus } from '@/lib/utils/status';
import { Badge, Divider } from '@ourtrip/ui';
import { HandCoins } from '@phosphor-icons/react';
import PaymentDetails from './payment-details';
import { convertNumberToCurrency } from '@/lib/utils/currency';

const OrderPayment = ({ order }: { order: Order }) => {
  return (
    <div className='bg-white p-4 rounded-default'>
      <div className='flex items-center gap-2'>
        <Badge
          size='small'
          type={orderPaymentStatus[order.status].type}
          icon={orderPaymentStatus[order.status].icon}
        >
          {orderPaymentStatus[order.status].text}
        </Badge>
        {order.tripcashInfo && (
          <Badge type='success' icon={<HandCoins weight='bold' />} size='small'>
            +{order.tripcashInfo.formatedValue} de Tripcash
          </Badge>
        )}
      </div>

      <div className='w-full flex flex-col mt-3 bg-gray-100 p-3 rounded-inner'>
        <div className='flex items-center justify-between'>
          <p className='text-sm text-gray-500'>Preço</p>
          <p className='text-sm text-gray-500'>
            {order.price?.currencySymbol} {order.price?.price.formattedValue}
          </p>
        </div>
        <div className='flex items-center justify-between'>
          <p className='text-sm text-gray-500'>Taxa</p>
          <p className='text-sm text-gray-500'>
            {order.price?.currencySymbol} {order.price?.taxValue.formattedValue}
          </p>
        </div>
        {order.price?.adjustments &&
          order.price?.adjustments.map(adjustment => (
            <div
              key={adjustment.value}
              className='flex items-center justify-between'
            >
              <p className='text-sm text-gray-500'>{adjustment.description}</p>
              <p className='text-sm text-gray-500'>
                - {order.price?.currencySymbol}{' '}
                {adjustment.formattedValue.replace('-', '')}
              </p>
            </div>
          ))}
        <Divider orientation='horizontal' className='mt-3' />
        <div className='flex items-center justify-between mt-2'>
          <p className='text-primary-900 font-medium'>Total</p>
          <h3 className='text-primary-900 text-lg font-semibold'>{`${order.price?.currencySymbol} ${order.price?.finalPrice.formattedValue}`}</h3>
        </div>
        {order.itens[0].booking.rooms.some(room => room.additionalTaxes) && (
          <div className='flex flex-col gap-1 mt-3'>
            <p className='text-sm text-primary-900'>Taxas Adicionais *</p>
            <p className='text-sm text-gray-500'>
              Os valores a serem pagos na propriedade foram calculados com base
              na cotação atual para facilitar a visualização. O valor correto
              será cobrado em EUR na propriedade.
            </p>
            {order.itens[0].booking.rooms.map(
              room =>
                room.additionalTaxes && (
                  <div key={room.accommodationId}>
                    {order.itens[0].booking.rooms.length > 1 && (
                      <p className='text-sm text-gray-500'>
                        Quarto {room.accommodationIndex + 1}
                      </p>
                    )}
                    <div className='flex gap-1'>
                      <p className='text-sm text-primary-900 font-medium'>
                        Pago na propriedade:
                      </p>
                      <p className='text-sm text-primary-900 font-medium'>
                        {room.additionalTaxes.originalCurrencyIncluded &&
                          room.additionalTaxes.originalAmountNotIncluded &&
                          `(${room.additionalTaxes.originalCurrencyIncluded} ${convertNumberToCurrency(
                            room.additionalTaxes.originalCurrencyIncluded,
                            room.additionalTaxes.originalAmountNotIncluded
                          )}) `}
                        {room.additionalTaxes.currencySymbol}{' '}
                        {convertNumberToCurrency(
                          room.additionalTaxes.currency,
                          room.additionalTaxes.amountNotIncluded
                        )}
                      </p>
                    </div>
                  </div>
                )
            )}
          </div>
        )}
      </div>
      <div className='flex items-center gap-3 mt-3 bg-gray-100 px-3 py-2 rounded-inner'>
        {orderPaymentApi[order.paymentResponse.paymentApi]?.image && (
          <div>
            <img
              className='w-20'
              src={orderPaymentApi[order.paymentResponse.paymentApi]?.image}
              alt='Logo API'
            />
          </div>
        )}
        {orderPaymentApi[order.paymentResponse.paymentApi]?.image && (
          <Divider />
        )}
        <PaymentDetails
          method={order.paymentMethod}
          currency={order.price?.currency}
          currencySymbol={order.price?.currencySymbol}
          transactionAmount={order.price?.finalPrice.value}
          paymentMethodId={order.payment?.paymentMethodId}
          installments={order.price?.installments}
          installmentAmount={order.price?.installmentAmount.value}
          usedTripcash={order.price?.adjustments[0]}
          status={order.status}
          payment={order.payment}
        />
      </div>
    </div>
  );
};

export default OrderPayment;
