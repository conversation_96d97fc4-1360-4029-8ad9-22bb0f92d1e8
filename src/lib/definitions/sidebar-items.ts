'use client';

import { SidebarItemProps } from '@/components/sidebar-item';
import {
  Calculator,
  ChartLineUp,
  CurrencyCircleDollar,
  GitBranch,
  IdentificationCard,
  Note,
  Pause,
  Robot,
  ShareNetwork,
  Tag,
  Ticket,
  Users
} from '@phosphor-icons/react';

export interface SidebarItem extends SidebarItemProps {
  items?: SidebarItem[];
  requiredPermissions?: string[];
  requireAny?: boolean;
}

export const sidebarItems: SidebarItem[] = [
  {
    href: '/dashboard',
    icon: ChartLineUp,
    label: 'Dashboard'
  },
  {
    href: '/orders',
    icon: Ticket,
    label: 'Pedidos'
  },
  {
    icon: Users,
    label: 'Usuários',
    items: [
      { href: '/users/customers', icon: Users, label: 'Clientes' },
      { href: '/users/admins', icon: Users, label: 'Administradores' },
      { href: '/users/profiles', icon: Users, label: 'Perfis' }
    ]
  },
  {
    href: '/tripcash',
    icon: Tag,
    label: 'Tripcash'
  },
  {
    icon: GitBranch,
    label: 'Fornecedores',
    items: [
      { href: '/suppliers/providers', icon: ShareNetwork, label: 'Provedores' },
      { href: '/suppliers/channels', icon: Pause, label: 'Canais' },
      {
        href: '/suppliers/providers-accounts',
        icon: IdentificationCard,
        label: 'Contas de Provedores'
      },
      { href: '/suppliers/rates', icon: CurrencyCircleDollar, label: 'Taxas' }
    ]
  },
  {
    icon: Calculator,
    label: 'Financeiro',
    href: '/finance',
    requiredPermissions: [
      'CAN_MANAGE_INVOICE',
      'CAN_VIEW_NFE',
      'CAN_ISSUE_NFE'
    ],
    requireAny: true,
    items: [
      {
        type: 'title',
        label: 'Notas Fiscais',
        requiredPermissions: ['CAN_VIEW_NFE', 'CAN_ISSUE_NFE'],
        requireAny: true
      },
      {
        href: '/finance/nfe/send',
        icon: Note,
        label: 'Emitir NFe',
        requiredPermissions: ['CAN_ISSUE_NFE']
      },
      {
        href: '/finance/nfe',
        icon: Note,
        label: 'Consulta',
        requiredPermissions: ['CAN_VIEW_NFE']
      },
      {
        type: 'title',
        label: 'Invoices',
        requiredPermissions: ['CAN_MANAGE_INVOICE']
      },
      {
        href: '/finance/invoices',
        icon: Note,
        label: 'Gerenciar',
        requiredPermissions: ['CAN_MANAGE_INVOICE']
      }
    ]
  },
  {
    icon: Robot,
    label: 'Timi',
    href: '/timi',
    tag: 'Beta',
    requiredPermissions: ['CAN_MANAGE_RATE']
  }
];

export function getSidebarItemsByPermissions(
  items: SidebarItem[],
  userPermissions: string[]
): SidebarItemProps[] {
  return items
    .filter(item => {
      if (!item.requiredPermissions || item.requiredPermissions.length === 0) {
        return true;
      }

      if (item.requireAny) {
        return item.requiredPermissions.some(perm =>
          userPermissions.includes(perm)
        );
      } else {
        return item.requiredPermissions.every(perm =>
          userPermissions.includes(perm)
        );
      }
    })
    .map(item => {
      if (item.items) {
        const filteredSubItems = getSidebarItemsByPermissions(
          item.items,
          userPermissions
        );
        return filteredSubItems.length > 0
          ? { ...item, items: filteredSubItems }
          : null;
      }
      return item;
    })
    .filter(Boolean) as SidebarItemProps[];
}
