export type Invoice = {
  idNfe: string;
  codPedido: string;
  codReserva: string;
  numeroNfe: string;
  status: string;
  dataEmissao: string;
  competencia: string;
  descricao: string;
  caminhoXml: string;
  urlDanfse: string;
};

export type InvoiceFilters = {
  page: number;
  size: number;
  orderCode?: string;
  reservationCode?: string;
  numeroNfe?: string;
  dataEmissaoInicio?: string;
  dataEmissaoFim?: string;
  dataCompetenciaInicio?: string;
  dataCompetenciaFim?: string;
};

export type InvoiceResponse = {
  content: Invoice[];
  pageable: {
    pageNumber: number;
    pageSize: number;
    sort: {
      direction: 'DESC' | 'ASC';
      property: string;
      ignoreCase: boolean;
      descending: boolean;
      ascending: boolean;
    };
    offset: number;
    paged: boolean;
    unpaged: boolean;
  };
  last: boolean;
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  sort: {
    direction: 'DESC' | 'ASC';
    property: string;
    ignoreCase: boolean;
    descending: boolean;
    ascending: boolean;
  };
  numberOfElements: number;
  first: boolean;
  empty: boolean;
};

export const defaultInvoiceFilters = {
  page: 0,
  size: 10,
  orderCode: '',
  reservationCode: '',
  numeroNfe: '',
  dataEmissaoInicio: '',
  dataEmissaoFim: '',
  dataCompetenciaInicio: '',
  dataCompetenciaFim: ''
};
