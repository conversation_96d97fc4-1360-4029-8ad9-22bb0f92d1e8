import { HotelOfferPlan, HotelPhoto } from './hotel';

export type BookingHotel = {
  id: string;
  name: string;
  stars: string;
  address: string;
  photoCover: HotelPhoto;
};

type BookingPriceDescription = {
  value: number;
  formattedValue: string;
  description: string;
};

export type BookingPrice = {
  price: BookingPriceDescription;
  pricePerNight: BookingPriceDescription;
  taxValue: BookingPriceDescription;
  priceWithTax: BookingPriceDescription;
  discount: BookingPriceDescription;
  priceWithDiscount: BookingPriceDescription;
  installments: number;
  installmentValue: BookingPriceDescription;
  finalPrice: BookingPriceDescription;
  currency: string;
  currencySymbol: string;
  tripcashInfo: BookingPriceDescription;
  orderItemCode?: string;
};

export type BookingGuest = {
  id: string;
  age: number;
  child: boolean;
  document: string;
  name: string;
  surname: string;
};

interface BookingAdditionalTaxesDetails {
  dateStart?: string;
  dateEnd?: string;
  description?: string;
  price: number;
  included: boolean;
}

export interface BookingAdditionalTaxes {
  amountIncluded: number;
  amountNotIncluded: number;
  currency: string;
  currencySymbol: string;
  originalAmountIncluded: number;
  originalAmountNotIncluded: number;
  originalCurrencyIncluded: string;
  originalCurrencyNotIncluded: string;
  originalTotalAmount: number;
  taxes: BookingAdditionalTaxesDetails[];
  totalAmount: number;
}

export interface BookingRoom
  extends Omit<
    HotelOfferPlan,
    'photos' | 'photoCover' | 'amenitiesGroups' | 'token' | 'offers'
  > {
  offerToken: string;
  accommodationIndex: number;
  mealPlanDisplay: string;
  guests: BookingGuest[];
  rateComments: string[];
  additionalTaxes: BookingAdditionalTaxes;
}

export enum BookingStatusEnum {
  CREATED = 'CREATED',
  BOOKING = 'BOOKING',
  BOOKED = 'BOOKED',
  FAILED = 'FAILED',
  DISCONTINUED = 'DISCONTINUED',
  PENDING_CANCEL = 'PENDING_CANCEL',
  CANCELED = 'CANCELED'
}

export type Booking = {
  id: string;
  status: BookingStatusEnum;
  statusDisplay: string;
  orderStatus: string;
  orderItemCode: string;
  checkin: string;
  checkout: string;
  reference: string;
  reservationCode: string;
  hotel: BookingHotel;
  price: BookingPrice;
  rooms: BookingRoom[];
};
