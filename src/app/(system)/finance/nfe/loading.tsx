import Table, { TableColumn } from '@/components/table';

export const tableColumns: TableColumn<string, any>[] = [
  {
    id: 'orderId',
    name: '<PERSON>ó<PERSON> do Pedido'
  },
  {
    id: 'number',
    name: '<PERSON><PERSON>mer<PERSON>'
  },
  {
    id: 'createdAt',
    name: '<PERSON> de Emissão'
  },
  {
    id: 'competence',
    name: 'Comp<PERSON>ência'
  },
  {
    id: 'status',
    name: 'Status'
  },
  {
    id: 'actions',
    name: 'Ações'
  }
];

const Loading = () => {
  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 relative overflow-auto scrollbar'>
      <div className='flex items-end gap-2 animate-pulse'>
        <div className='w-32 h-7 bg-gray-200 rounded-inner' />
        <div className='w-6 h-6 bg-gray-200 rounded-full' />
      </div>
      <div className='w-full flex items-start gap-4'>
        <div className='w-full flex flex-col gap-3'>
          <div className='w-full flex justify-between items-end'>
            <div className='w-20 h-8 bg-gray-200 animate-pulse rounded-inner' />
            <div className='w-44 h-5 bg-gray-200 animate-pulse rounded-inner' />
          </div>
          <Table columns={tableColumns} items={[]} loading={true} />
        </div>
      </div>
    </div>
  );
};

export default Loading;
