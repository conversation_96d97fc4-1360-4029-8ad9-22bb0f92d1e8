import InvoicesCards from '@/components/invoices-cards';
import InvoicesHeader from '@/components/invoices-header';
import RefreshButton from '@/components/refresh-button';
import SendNFe from '@/components/send-nfe';
import Table, { TableColumn } from '@/components/table';
import { convertStringToUTCDate } from '@/lib/utils/date';
import {
  getInvoicesErrors,
  getInvoiceSubmissionStatus
} from '@/services/invoice';
import { format } from 'date-fns';

const tableColumns: TableColumn<string, any>[] = [
  {
    id: 'orderId',
    name: '<PERSON>ó<PERSON> do Pedido'
  },
  {
    id: 'createdAt',
    name: 'Data de Emissão'
  },
  {
    id: 'competence',
    name: 'Co<PERSON>et<PERSON><PERSON>'
  },
  {
    id: 'error',
    name: 'Erro'
  }
];

const SendInvoice = async () => {
  const invoiceSubmissionStatusResponse = await getInvoiceSubmissionStatus();
  const invoiceErrorsResponse = await getInvoicesErrors({ page: 0, size: 10 });

  // await new Promise(resolve => setTimeout(resolve, 100000));

  if (invoiceSubmissionStatusResponse.error)
    throw new Error(invoiceSubmissionStatusResponse.error);
  if (invoiceErrorsResponse.error) throw new Error(invoiceErrorsResponse.error);

  const invoiceSubmissionStatus = invoiceSubmissionStatusResponse.data!;
  const invoiceErrors = invoiceErrorsResponse.data!;

  const tableItems: any[] =
    invoiceErrors.content?.map(invoice => ({
      orderId: (
        <p className='text-primary-900 text-sm font-medium'>
          {invoice.codPedido}
        </p>
      ),
      number: (
        <p className='text-gray-500 text-sm'>{invoice.numeroNfe ?? '-'}</p>
      ),
      createdAt: (
        <p className='text-gray-500 text-sm'>
          {invoice.dataEmissao
            ? format(
                convertStringToUTCDate(invoice.dataEmissao),
                'dd/MM/yyyy HH:mm'
              )
            : '-'}
        </p>
      ),
      competence: (
        <p className='text-gray-500 text-sm'>
          {format(convertStringToUTCDate(invoice.competencia), 'MM/yyyy')}
        </p>
      ),
      error: <p className='text-gray-500 text-sm'>{invoice.descricao}</p>
    })) ?? [];

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 relative overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Notas Fiscais</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex items-start gap-6'>
        <div className='w-full flex flex-col gap-3'>
          <InvoicesCards
            invoiceSubmissionStatus={invoiceSubmissionStatus}
            errors={invoiceErrors}
          />
          <Table columns={tableColumns} items={tableItems} />
        </div>
        <SendNFe />
      </div>
    </div>
  );
};

export default SendInvoice;
