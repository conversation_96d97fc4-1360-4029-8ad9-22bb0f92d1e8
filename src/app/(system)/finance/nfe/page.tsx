import RefreshButton from '@/components/refresh-button';
import InvoicesHeader from '@/components/invoices-header';
import { getInvoices } from '@/services/invoice';
import { InvoiceFilters } from '@/lib/definitions/invoice';
import Table from '@/components/table';
import { tableColumns } from './loading';
import { convertStringToUTCDate } from '@/lib/utils/date';
import { format } from 'date-fns';
import InvoicesActions from '@/components/invoices-actions';
import InvoicesFooter from '@/components/invoices-footer';

interface InvoicesProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

const Invoices = async ({ searchParams }: InvoicesProps) => {
  const params = await searchParams;

  const payload = {
    page: parseInt(params.page?.toString() ?? '0', 10),
    size: parseInt(params.size?.toString() ?? '10', 10),
    orderCode: params.orderCode || '',
    reservationCode: params.reservationCode || '',
    numeroNfe: params.numeroNfe || '',
    dataEmissaoInicio: params.dataEmissaoInicio || '',
    dataEmissaoFim: params.dataEmissaoFim || '',
    dataCompetenciaInicio: params.dataCompetenciaInicio || '',
    dataCompetenciaFim: params.dataCompetenciaFim || ''
  };

  const response = await getInvoices(payload as InvoiceFilters);

  if (response.error) {
    throw new Error(response.error);
  }
  const invoices = response.data!;

  const tableItems: any[] =
    invoices?.content?.map(invoice => ({
      orderId: (
        <p className='text-primary-900 text-sm font-medium'>
          {invoice.codPedido}
        </p>
      ),
      number: (
        <p className='text-gray-500 text-sm'>{invoice.numeroNfe ?? '-'}</p>
      ),
      createdAt: (
        <p className='text-gray-500 text-sm'>
          {invoice.dataEmissao
            ? format(
                convertStringToUTCDate(invoice.dataEmissao),
                'dd/MM/yyyy HH:mm'
              )
            : '-'}
        </p>
      ),
      competence: (
        <p className='text-gray-500 text-sm'>
          {format(convertStringToUTCDate(invoice.competencia), 'MM/yyyy')}
        </p>
      ),
      status: <p className='text-gray-500 text-sm'>{invoice.status}</p>,
      actions: <InvoicesActions invoice={invoice} />
    })) ?? [];

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 relative overflow-auto scrollbar'>
      <div className='flex items-end gap-2'>
        <h1 className='text-xl font-semibold'>Notas Fiscais</h1>
        <RefreshButton />
      </div>
      <div className='w-full flex flex-col gap-4 justify-between'>
        <InvoicesHeader invoices={invoices} />
        <Table columns={tableColumns} items={tableItems} loading={false} />
        <InvoicesFooter invoices={invoices} />
      </div>
    </div>
  );
};

export default Invoices;
