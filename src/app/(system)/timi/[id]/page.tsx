import OrderBackButton from '@/components/order-back-button';
import RefreshButton from '@/components/refresh-button';
import RunDetailsPrice from '@/components/run-details-price';
import RunDetailStatus from '@/components/run-details-status';
import StatCard from '@/components/stat-card';
import Table, { TableColumn } from '@/components/table';
import { convertNumberToCurrency } from '@/lib/utils/currency';
import { convertStringToFormatedDateWithDate } from '@/lib/utils/date';
import { createClient } from '@/services/supabase';
import { Dot, Target } from '@phosphor-icons/react/dist/ssr';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);

const hotelsColumns: TableColumn<string, any>[] = [
  { id: 'hotel_id', name: 'ID', className: 'w-10' },
  { id: 'hotel_name', name: 'Hotel' },
  { id: 'price', name: 'Preço OurTrip' },
  { id: 'target_price', name: 'Target', className: '!pl-0' },
  { id: 'new_tax', name: 'Nova Taxa' },
  { id: 'new_markup', name: 'Novo Markup' },
  { id: 'cpa', name: 'CPA' },
  { id: 'success', name: 'Status', className: 'flex justify-end' }
];

const Run = async ({ params }: { params: Promise<{ id: string }> }) => {
  const { id } = await params;
  const supabase = await createClient();

  const { data: run } = await supabase
    .from('run')
    .select('*')
    .eq('id', id)
    .single();

  const { data: metrics } = await supabase
    .from('vw_run_metrics')
    .select('*')
    .eq('run_id', id)
    .single();

  const { data: hotels } = await supabase
    .from('run_hotel_change')
    .select('*')
    .eq('run_id', id);

  const hotelsList: any[] =
    hotels?.map(hotel => ({
      hotel_id: <p className='text-sm text-gray-500'>{hotel.hotel_id}</p>,
      hotel_name: (
        <p className='text-sm text-primary-900 font-medium overflow-hidden text-ellipsis whitespace-nowrap'>
          {hotel.hotel_name.length > 10
            ? `${hotel.hotel_name.slice(0, 10)}...`
            : hotel.hotel_name}
        </p>
      ),
      price: <RunDetailsPrice hotel={hotel} />,
      target_price: (
        <div className='flex items-center gap-1'>
          <Target />
          <p className='text-sm text-gray-500'>{`R$ ${convertNumberToCurrency('BRL', hotel.target_price * 100)}`}</p>
        </div>
      ),
      new_tax: <p className='text-sm text-gray-500'>{hotel.new_tax}</p>,
      new_markup: <p className='text-sm text-gray-500'>{hotel.new_markup}</p>,
      cpa: <p className='text-sm text-gray-500'>{hotel.cpa}%</p>,
      success: <RunDetailStatus hotel={hotel} />
    })) || [];

  return (
    <div className='w-full flex flex-col gap-4 py-12 pr-12 overflow-auto scrollbar'>
      <OrderBackButton />
      <div className='flex items-baseline'>
        <h1 className='flex items-center text-xl font-semibold'>
          Execução {run.id.split('-')[0]}
          <span className='text-gray-500 text-sm font-normal ml-2'>
            {convertStringToFormatedDateWithDate(run.created_at)}
          </span>
          <Dot size={24} className='text-gray-500' weight='bold' />
        </h1>
        <RefreshButton />
      </div>
      <div className='flex flex-col gap-2'>
        <div className='flex gap-2'>
          <StatCard
            value={`${metrics.total_changes || 0}/${run.processed_hotels_count || 0}`}
            label='Hotéis'
          />
          <StatCard
            value={metrics.success_count || 0}
            label='Alterações com Sucesso'
          />
          <StatCard
            value={metrics.failure_count || 0}
            label='Alterações com Falha'
          />
          <StatCard
            value={
              dayjs(run.ended_at).diff(dayjs(run.created_at), 'second') >= 0
                ? dayjs
                    .utc(dayjs(run.ended_at).diff(dayjs(run.created_at)))
                    .format('HH:mm:ss')
                : '00:00:00'
            }
            label='Tempo'
          />
        </div>
        <div className='flex gap-2'>
          <StatCard
            value={`${metrics.success_rate_pct || 0}%`}
            label='Taxa de sucesso'
          />
          <StatCard
            value={`${metrics.failure_rate_pct || 0}%`}
            label='Taxa de falha'
          />
          <StatCard value={`${metrics.beat_rate_pct || 0}%`} label='Beat' />
        </div>
      </div>
      <div className='flex'>
        <Table columns={hotelsColumns} items={hotelsList} />
      </div>
    </div>
  );
};

export default Run;
