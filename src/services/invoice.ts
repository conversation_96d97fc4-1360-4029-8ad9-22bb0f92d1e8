'use server';

import { verifySession } from '@/actions/dal';
import { InvoiceFilters, InvoiceResponse } from '@/lib/definitions/invoice';
import { redirect } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333';

export const loadInvoices = async (
  formData: FormData
): Promise<{
  data?: string;
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/nfe/load`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${session.token}`
    },
    body: formData
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para carregar notas fiscais'
      };
    }

    return {
      error: 'Falha ao buscar notas fiscais'
    };
  }

  return {
    data: await response.text()
  };
};

export const updatePendingInvoices = async (): Promise<{
  data?: boolean;
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/nfe`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para atualizar notas fiscais'
      };
    }

    return {
      error: 'Falha ao atualizar notas fiscais'
    };
  }

  return {
    data: response.ok
  };
};

export const resendErrorsInvoices = async (
  ids: string[]
): Promise<{
  data?: boolean;
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/nfe/resend`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${session.token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(ids)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para reenviar erros de notas fiscais'
      };
    }

    const responseBody = await response.json();
    if (responseBody.message) {
      return {
        error: responseBody.message
      };
    }

    return {
      error: 'Falha ao reenviar erros de notas fiscais'
    };
  }

  return {
    data: response.ok
  };
};

export const getInvoicesErrors = async (payload: {
  page: number;
  size: number;
}): Promise<{ data?: InvoiceResponse; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/nfe/listWithError`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${session.token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver notas fiscais com erro'
      };
    }

    return {
      error: 'Falha ao buscar notas fiscais com erro'
    };
  }

  return {
    data: await response.json()
  };
};

export const getInvoices = async (
  filters: InvoiceFilters
): Promise<{ data?: InvoiceResponse; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/nfe/list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    },
    body: JSON.stringify(filters)
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver notas fiscais'
      };
    }

    return {
      error: 'Falha ao buscar notas fiscais'
    };
  }

  return {
    data: await response.json()
  };
};

export const processPendingInvoices = async (): Promise<{
  data?: boolean;
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/nfe/send`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para processar notas fiscais'
      };
    }

    return {
      error: 'Falha ao processar notas fiscais'
    };
  }

  return {
    data: response.ok
  };
};

export const getInvoiceSubmissionStatus = async (): Promise<{
  data?: any;
  error?: string;
}> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/nfe/groupUnfinishedByStatus`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver notas fiscais'
      };
    }

    return {
      error: 'Falha ao buscar notas fiscais'
    };
  }

  return {
    data: await response.json()
  };
};

export const getInvoiceFile = async (
  id: string
): Promise<{ data?: ArrayBuffer; error?: string }> => {
  const session = await verifySession();
  if (!session) redirect('/');

  const response = await fetch(`${API_URL}/admin/nfe/danfe/${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session.token}`
    }
  });

  if (!response.ok) {
    if (response.status === 403) {
      return {
        error: 'Você não tem permissão para ver notas fiscais'
      };
    }

    return {
      error: 'Falha ao buscar notas fiscais'
    };
  }

  return {
    data: await response.arrayBuffer()
  };
};
